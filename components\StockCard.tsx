import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Stock } from '../app/api';

interface StockCardProps {
  stock: Stock;
}

// Function to calculate dividend per 100 shares
const calculateDividend = (diviCash: string): string => {
  const cash = parseFloat(diviCash.replace('元', ''));
  if (isNaN(cash)) {
    return diviCash; // Return original if parsing fails
  }
  // Assuming the API provides dividend per 10 shares, common in CN market
  return `${(cash * 10).toFixed(2)}元`;
};

const StockCard: React.FC<StockCardProps> = ({ stock }) => {
  return (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <Text style={styles.name}>{stock.name}</Text>
        <Text style={styles.code}>{stock.code}</Text>
      </View>
      <View style={styles.cardBody}>
        <Text style={styles.dividendLabel}>每100股派</Text>
        <Text style={styles.dividendValue}>{calculateDividend(stock.diviCash)}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginVertical: 5,
    marginHorizontal: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  name: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  code: {
    fontSize: 12,
    color: '#888',
    marginLeft: 8,
  },
  cardBody: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    marginTop: 10,
  },
  dividendLabel: {
    fontSize: 14,
    color: '#555',
  },
  dividendValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#d9534f',
  },
});

export default StockCard;
