import { Tabs } from "expo-router";
import { useEffect } from "react";
import { registerBackgroundFetchAsync } from "./notifications";

export default function AppLayout() {
  useEffect(() => {
    registerBackgroundFetchAsync();
  }, []);

  return (
    <Tabs
      screenOptions={{
        tabBarIconStyle: { display: "none" },
        tabBarLabelStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
        tabBarStyle: {
            height: 80,
            paddingBottom: 16,
        }
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: "日历",
        }}
      />
      <Tabs.Screen
        name="following"
        options={{
          title: "关注",
        }}
      />
    </Tabs>
  );
}
