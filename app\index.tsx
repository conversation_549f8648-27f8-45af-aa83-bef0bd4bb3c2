import { Text, View, FlatList, ActivityIndicator, TouchableOpacity, StyleSheet } from "react-native";
import { useState, useCallback } from "react";
import { getFinanceCalendar, CalendarInfo } from "./api";
import StockCard from "../components/StockCard";
import { useFocusEffect } from "expo-router";
import { useFollowingStore } from "./store";

const DailyDividends = ({ item }: { item: CalendarInfo }) => {
  const [expanded, setExpanded] = useState(false);
  const { following } = useFollowingStore();

  if (!item.list || item.list.length === 0) {
    return (
      <View style={styles.dayContainer}>
        <View style={styles.dateHeaderContainer}>
            <Text style={styles.dateHeader}>{item.dateStr}</Text>
            <Text style={styles.noDividendBadge}>无分红</Text>
        </View>
      </View>
    );
  }

  const followedStocks = item.list.filter(stock => following.includes(stock.code));
  const unfollowedStocks = item.list.filter(stock => !following.includes(stock.code));

  return (
    <View style={styles.dayContainer}>
      <View style={styles.dateHeaderContainer}>
        <Text style={styles.dateHeader}>{item.dateStr}</Text>
        {followedStocks.length === 0 && <Text style={styles.noDividendBadge}>无关注</Text>}
      </View>
      {followedStocks.length > 0 && followedStocks.map(stock => <StockCard key={stock.code} stock={stock} />)}

      {unfollowedStocks.length > 0 && (
        <TouchableOpacity onPress={() => setExpanded(!expanded)} style={styles.toggleButton}>
          <Text style={styles.toggleButtonText}>{expanded ? "收起" : "展开"} ({unfollowedStocks.length})</Text>
        </TouchableOpacity>
      )}

      {expanded && unfollowedStocks.map(stock => <StockCard key={stock.code} stock={stock} />)}
    </View>
  );
};

export default function Index() {
  const [data, setData] = useState<CalendarInfo[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchData = async () => {
    setLoading(true);
    const today = new Date();
    const sevenDaysFromNow = new Date();
    sevenDaysFromNow.setDate(today.getDate() + 30); // Fetch for 30 days

    const startDate = today.toISOString().split('T')[0];
    const endDate = sevenDaysFromNow.toISOString().split('T')[0];

    try {
      const result = await getFinanceCalendar(startDate, endDate);
      setData(result);
    } catch (error) {
      console.error("Failed to fetch calendar data:", error);
      // Optionally, set some error state to show in the UI
    }
    setLoading(false);
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <FlatList
      data={data}
      keyExtractor={(item) => item.date}
      renderItem={({ item }) => <DailyDividends item={item} />}
      contentContainerStyle={styles.listContainer}
    />
  );
}

const styles = StyleSheet.create({
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: '#f0f0f0',
  },
  listContainer: {
    paddingVertical: 5,
    backgroundColor: '#f0f0f0',
  },
  dayContainer: {
    marginBottom: 10,
  },
  dateHeaderContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 15,
      paddingVertical: 8,
  },
  dateHeader: {
    fontSize: 16,
    fontWeight: "bold",
    color: '#333',
  },
  badgeContainer: {
    paddingHorizontal: 10,
    paddingBottom: 5,
  },
  noDividendBadge: {
      backgroundColor: '#e7e7e7',
      color: '#888',
      fontSize: 12,
      paddingHorizontal: 8,
      paddingVertical: 3,
      borderRadius: 5,
      overflow: 'hidden',
      alignSelf: 'flex-start',
  },
  toggleButton: {
    backgroundColor: '#f0f0f0',
    padding: 8,
    marginHorizontal: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 5,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  toggleButtonText: {
    color: '#555',
    fontWeight: '500',
    fontSize: 12,
  },
});
