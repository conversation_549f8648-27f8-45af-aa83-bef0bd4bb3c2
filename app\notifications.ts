import * as Notifications from 'expo-notifications';
import * as TaskManager from 'expo-task-manager';
import { getFinanceCalendar } from './api';
import { useFollowingStore } from './store';

const BACKGROUND_NOTIFICATION_TASK = 'BACKGROUND-NOTIFICATION-TASK';

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

TaskManager.defineTask(BACKGROUND_NOTIFICATION_TASK, async () => {
  try {
    const { following } = useFollowingStore.getState();
    if (following.length === 0) {
      return;
    }

    const todayStr = new Date().toISOString().split('T')[0];
    const calendar = await getFinanceCalendar(todayStr, todayStr);
    const todayDividends = calendar.find(c => c.date === todayStr);

    if (todayDividends && todayDividends.list) {
      for (const stock of todayDividends.list) {
        if (following.includes(stock.code)) {
          await Notifications.scheduleNotificationAsync({
            content: {
              title: `分红提醒: ${stock.name} (${stock.code})`,
              body: `今日有分红，每100股派 ${stock.diviCash}`,
            },
            trigger: { hour: 20, minute: 0, repeats: false },
          });
        }
      }
    }
  } catch (error) {
    console.error('Background notification task failed:', error);
  }
});

export const registerBackgroundFetchAsync = async () => {
  await Notifications.cancelAllScheduledNotificationsAsync(); // Clear old notifications

  const isRegistered = await TaskManager.isTaskRegisteredAsync(BACKGROUND_NOTIFICATION_TASK);
  if (isRegistered) {
    await TaskManager.unregisterTaskAsync(BACKGROUND_NOTIFICATION_TASK);
  }

  await Notifications.registerTaskAsync(BACKGROUND_NOTIFICATION_TASK, {
      taskName: BACKGROUND_NOTIFICATION_TASK,
      startOnBoot: true,
      periodic: true,
      interval: 24 * 60 * 60, // Run once a day
  });
};
