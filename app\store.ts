import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface FollowingState {
  following: string[];
  toggleFollow: (code: string) => void;
}

export const useFollowingStore = create<FollowingState>()(
  persist(
    (set) => ({
      following: [],
      toggleFollow: (code) =>
        set((state) => ({
          following: state.following.includes(code)
            ? state.following.filter((c) => c !== code)
            : [...state.following, code],
        })),
    }),
    {
      name: 'following-storage', // unique name
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
